{"genesis": {"fileFormatVersion": 6, "address": "0x3", "name": "genesis", "friends": [], "structs": {"GenesisChainParameters": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "protocol_version", "type": "U64"}, {"name": "chain_start_timestamp_ms", "type": "U64"}, {"name": "epoch_duration_ms", "type": "U64"}, {"name": "stake_subsidy_start_epoch", "type": "U64"}, {"name": "stake_subsidy_initial_distribution_amount", "type": "U64"}, {"name": "stake_subsidy_period_length", "type": "U64"}, {"name": "stake_subsidy_decrease_rate", "type": "U16"}, {"name": "max_validator_count", "type": "U64"}, {"name": "min_validator_joining_stake", "type": "U64"}, {"name": "validator_low_stake_threshold", "type": "U64"}, {"name": "validator_very_low_stake_threshold", "type": "U64"}, {"name": "validator_low_stake_grace_period", "type": "U64"}]}, "GenesisValidatorMetadata": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "name", "type": {"Vector": "U8"}}, {"name": "description", "type": {"Vector": "U8"}}, {"name": "image_url", "type": {"Vector": "U8"}}, {"name": "project_url", "type": {"Vector": "U8"}}, {"name": "sui_address", "type": "Address"}, {"name": "gas_price", "type": "U64"}, {"name": "commission_rate", "type": "U64"}, {"name": "protocol_public_key", "type": {"Vector": "U8"}}, {"name": "proof_of_possession", "type": {"Vector": "U8"}}, {"name": "network_public_key", "type": {"Vector": "U8"}}, {"name": "worker_public_key", "type": {"Vector": "U8"}}, {"name": "network_address", "type": {"Vector": "U8"}}, {"name": "p2p_address", "type": {"Vector": "U8"}}, {"name": "primary_address", "type": {"Vector": "U8"}}, {"name": "worker_address", "type": {"Vector": "U8"}}]}, "TokenAllocation": {"abilities": {"abilities": []}, "typeParameters": [], "fields": [{"name": "recipient_address", "type": "Address"}, {"name": "amount_mist", "type": "U64"}, {"name": "staked_with_validator", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["Address"]}}}]}, "TokenDistributionSchedule": {"abilities": {"abilities": []}, "typeParameters": [], "fields": [{"name": "stake_subsidy_fund_mist", "type": "U64"}, {"name": "allocations", "type": {"Vector": {"Struct": {"address": "0x3", "module": "genesis", "name": "TokenAllocation", "typeArguments": []}}}}]}}, "exposedFunctions": {}}, "stake_subsidy": {"fileFormatVersion": 6, "address": "0x3", "name": "stake_subsidy", "friends": [{"address": "0x3", "name": "genesis"}, {"address": "0x3", "name": "sui_system_state_inner"}], "structs": {"StakeSubsidy": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "balance", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "distribution_counter", "type": "U64"}, {"name": "current_distribution_amount", "type": "U64"}, {"name": "stake_subsidy_period_length", "type": "U64"}, {"name": "stake_subsidy_decrease_rate", "type": "U16"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}}, "exposedFunctions": {"advance_epoch": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "create": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", "U64", "U16", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}]}, "current_epoch_subsidy_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}}], "return": ["U64"]}, "get_distribution_counter": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}}], "return": ["U64"]}}}, "staking_pool": {"fileFormatVersion": 6, "address": "0x3", "name": "staking_pool", "friends": [{"address": "0x3", "name": "validator"}, {"address": "0x3", "name": "validator_set"}], "structs": {"FungibleStakedSui": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "value", "type": "U64"}]}, "FungibleStakedSuiData": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "total_supply", "type": "U64"}, {"name": "principal", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}]}, "FungibleStakedSuiDataKey": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "dummy_field", "type": "Bool"}]}, "PoolTokenExchangeRate": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "sui_amount", "type": "U64"}, {"name": "pool_token_amount", "type": "U64"}]}, "StakedSui": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "stake_activation_epoch", "type": "U64"}, {"name": "principal", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}]}, "StakingPool": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "activation_epoch", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}}, {"name": "deactivation_epoch", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}}, {"name": "sui_balance", "type": "U64"}, {"name": "rewards_pool", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "pool_token_balance", "type": "U64"}, {"name": "exchange_rates", "type": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U64", {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}}}, {"name": "pending_stake", "type": "U64"}, {"name": "pending_total_sui_withdraw", "type": "U64"}, {"name": "pending_pool_token_withdraw", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}}, "exposedFunctions": {"activate_staking_pool": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, "U64"], "return": []}, "convert_to_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "deactivate_staking_pool": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, "U64"], "return": []}, "deposit_rewards": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}], "return": []}, "exchange_rates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U64", {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}}}]}, "fungible_staked_sui_pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "fungible_staked_sui_value": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}}], "return": ["U64"]}, "is_equal_staking_metadata": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}], "return": ["Bool"]}, "is_inactive": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": ["Bool"]}, "is_preactive": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": ["Bool"]}, "join_fungible_staked_sui": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}], "return": []}, "join_staked_sui": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}], "return": []}, "new": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}]}, "pending_stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": ["U64"]}, "pending_stake_withdraw_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": ["U64"]}, "pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "pool_token_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}}], "return": ["U64"]}, "pool_token_exchange_rate_at_epoch": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, "U64"], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}, "process_pending_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": []}, "process_pending_stakes_and_withdraws": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "redeem_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "request_add_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_withdraw_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "split": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "split_fungible_staked_sui": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "split_staked_sui": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "stake_activation_epoch": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}], "return": ["U64"]}, "staked_sui_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}}], "return": ["U64"]}, "sui_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}}], "return": ["U64"]}, "sui_balance": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}], "return": ["U64"]}, "withdraw_from_principal": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}], "return": ["U64", {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}}}, "storage_fund": {"fileFormatVersion": 6, "address": "0x3", "name": "storage_fund", "friends": [{"address": "0x3", "name": "sui_system_state_inner"}], "structs": {"StorageFund": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "total_object_storage_rebates", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "non_refundable_balance", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}]}}, "exposedFunctions": {"advance_epoch": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", "U64"], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "new": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}], "return": [{"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}]}, "total_balance": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}}], "return": ["U64"]}, "total_object_storage_rebates": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}}], "return": ["U64"]}}}, "sui_system": {"fileFormatVersion": 6, "address": "0x3", "name": "sui_system", "friends": [{"address": "0x3", "name": "genesis"}], "structs": {"SuiSystemState": {"abilities": {"abilities": ["Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "version", "type": "U64"}]}}, "exposedFunctions": {"active_validator_addresses": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}], "return": [{"Vector": "Address"}]}, "convert_to_fungible_staked_sui": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "create": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}, {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", "U64", {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SystemParameters", "typeArguments": []}}, {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "pool_exchange_rates": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U64", {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}}}]}, "redeem_fungible_staked_sui": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "report_validator": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "Address"], "return": []}, "request_add_stake": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "coin", "name": "Coin", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_add_stake_mul_coin": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": {"Struct": {"address": "0x2", "module": "coin", "name": "Coin", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_add_stake_non_entry": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "coin", "name": "Coin", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_add_validator": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_add_validator_candidate": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator_candidate": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_commission_rate": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_gas_price": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "U64"], "return": []}, "request_withdraw_stake": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_withdraw_stake_non_entry": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "rotate_operation_cap": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "set_candidate_validator_commission_rate": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "set_candidate_validator_gas_price": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "U64"], "return": []}, "undo_report_validator": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "Address"], "return": []}, "update_candidate_validator_network_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_network_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_p2p_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_primary_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_protocol_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_worker_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_worker_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_description": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_image_url": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_name": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_network_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_network_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_p2p_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_primary_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_protocol_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_worker_address": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_worker_pubkey": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_project_url": {"visibility": "Public", "isEntry": true, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "validator_address_by_pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system", "name": "SuiSystemState", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": ["Address"]}}}, "sui_system_state_inner": {"fileFormatVersion": 6, "address": "0x3", "name": "sui_system_state_inner", "friends": [{"address": "0x3", "name": "genesis"}, {"address": "0x3", "name": "sui_system"}], "structs": {"SuiSystemStateInner": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "protocol_version", "type": "U64"}, {"name": "system_state_version", "type": "U64"}, {"name": "validators", "type": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"name": "storage_fund", "type": {"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}}, {"name": "parameters", "type": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SystemParameters", "typeArguments": []}}}, {"name": "reference_gas_price", "type": "U64"}, {"name": "validator_report_records", "type": {"Struct": {"address": "0x2", "module": "vec_map", "name": "VecMap", "typeArguments": ["Address", {"Struct": {"address": "0x2", "module": "vec_set", "name": "VecSet", "typeArguments": ["Address"]}}]}}}, {"name": "stake_subsidy", "type": {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}}, {"name": "safe_mode", "type": "Bool"}, {"name": "safe_mode_storage_rewards", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "safe_mode_computation_rewards", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "safe_mode_storage_rebates", "type": "U64"}, {"name": "safe_mode_non_refundable_storage_fee", "type": "U64"}, {"name": "epoch_start_timestamp_ms", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}, "SuiSystemStateInnerV2": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "protocol_version", "type": "U64"}, {"name": "system_state_version", "type": "U64"}, {"name": "validators", "type": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"name": "storage_fund", "type": {"Struct": {"address": "0x3", "module": "storage_fund", "name": "StorageFund", "typeArguments": []}}}, {"name": "parameters", "type": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SystemParametersV2", "typeArguments": []}}}, {"name": "reference_gas_price", "type": "U64"}, {"name": "validator_report_records", "type": {"Struct": {"address": "0x2", "module": "vec_map", "name": "VecMap", "typeArguments": ["Address", {"Struct": {"address": "0x2", "module": "vec_set", "name": "VecSet", "typeArguments": ["Address"]}}]}}}, {"name": "stake_subsidy", "type": {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}}, {"name": "safe_mode", "type": "Bool"}, {"name": "safe_mode_storage_rewards", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "safe_mode_computation_rewards", "type": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"name": "safe_mode_storage_rebates", "type": "U64"}, {"name": "safe_mode_non_refundable_storage_fee", "type": "U64"}, {"name": "epoch_start_timestamp_ms", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}, "SystemEpochInfoEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "protocol_version", "type": "U64"}, {"name": "reference_gas_price", "type": "U64"}, {"name": "total_stake", "type": "U64"}, {"name": "storage_fund_reinvestment", "type": "U64"}, {"name": "storage_charge", "type": "U64"}, {"name": "storage_rebate", "type": "U64"}, {"name": "storage_fund_balance", "type": "U64"}, {"name": "stake_subsidy_amount", "type": "U64"}, {"name": "total_gas_fees", "type": "U64"}, {"name": "total_stake_rewards_distributed", "type": "U64"}, {"name": "leftover_storage_fund_inflow", "type": "U64"}]}, "SystemParameters": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "epoch_duration_ms", "type": "U64"}, {"name": "stake_subsidy_start_epoch", "type": "U64"}, {"name": "max_validator_count", "type": "U64"}, {"name": "min_validator_joining_stake", "type": "U64"}, {"name": "validator_low_stake_threshold", "type": "U64"}, {"name": "validator_very_low_stake_threshold", "type": "U64"}, {"name": "validator_low_stake_grace_period", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}, "SystemParametersV2": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "epoch_duration_ms", "type": "U64"}, {"name": "stake_subsidy_start_epoch", "type": "U64"}, {"name": "min_validator_count", "type": "U64"}, {"name": "max_validator_count", "type": "U64"}, {"name": "min_validator_joining_stake", "type": "U64"}, {"name": "validator_low_stake_threshold", "type": "U64"}, {"name": "validator_very_low_stake_threshold", "type": "U64"}, {"name": "validator_low_stake_grace_period", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}}, "exposedFunctions": {"active_validator_addresses": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": [{"Vector": "Address"}]}, "active_validator_voting_powers": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "vec_map", "name": "VecMap", "typeArguments": ["Address", "U64"]}}]}, "advance_epoch": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "U64", "U64", {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", "U64", "U64", "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "convert_to_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "create": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "U64", "U64", {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SystemParameters", "typeArguments": []}}, {"Struct": {"address": "0x3", "module": "stake_subsidy", "name": "StakeSubsidy", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInner", "typeArguments": []}}]}, "create_system_parameters": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64", "U64", "U64", "U64", "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SystemParameters", "typeArguments": []}}]}, "epoch": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "epoch_start_timestamp_ms": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "genesis_system_state_version": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [], "return": ["U64"]}, "get_reporters_of": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "Address"], "return": [{"Struct": {"address": "0x2", "module": "vec_set", "name": "VecSet", "typeArguments": ["Address"]}}]}, "get_storage_fund_object_rebates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "get_storage_fund_total_balance": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "pool_exchange_rates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U64", {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}}}]}, "protocol_version": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "redeem_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "report_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "Address"], "return": []}, "request_add_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "coin", "name": "Coin", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_add_stake_mul_coin": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": {"Struct": {"address": "0x2", "module": "coin", "name": "Coin", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_add_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_add_validator_candidate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator_candidate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_commission_rate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "U64", {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_gas_price": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "U64"], "return": []}, "request_withdraw_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "rotate_operation_cap": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "set_candidate_validator_commission_rate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "U64", {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "set_candidate_validator_gas_price": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "U64"], "return": []}, "store_execution_time_estimates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "system_state_version": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": ["U64"]}, "undo_report_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "Address"], "return": []}, "update_candidate_validator_network_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_network_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_p2p_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_primary_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_protocol_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_worker_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_candidate_validator_worker_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_description": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_image_url": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_name": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_network_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_network_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_p2p_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_primary_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_protocol_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_worker_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_next_epoch_worker_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "update_validator_project_url": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Vector": "U8"}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "v1_to_v2": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInner", "typeArguments": []}}], "return": [{"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}]}, "validator_address_by_pool_id": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": ["Address"]}, "validator_stake_amount": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "Address"], "return": ["U64"]}, "validator_staking_pool_id": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}, "Address"], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "validator_staking_pool_mappings": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "sui_system_state_inner", "name": "SuiSystemStateInnerV2", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}, "Address"]}}}]}}}, "validator": {"fileFormatVersion": 6, "address": "0x3", "name": "validator", "friends": [{"address": "0x3", "name": "genesis"}, {"address": "0x3", "name": "sui_system_state_inner"}, {"address": "0x3", "name": "validator_set"}, {"address": "0x3", "name": "voting_power"}], "structs": {"ConvertingToFungibleStakedSuiEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "stake_activation_epoch", "type": "U64"}, {"name": "staked_sui_principal_amount", "type": "U64"}, {"name": "fungible_staked_sui_amount", "type": "U64"}]}, "RedeemingFungibleStakedSuiEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "fungible_staked_sui_amount", "type": "U64"}, {"name": "sui_amount", "type": "U64"}]}, "StakingRequestEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "validator_address", "type": "Address"}, {"name": "staker_address", "type": "Address"}, {"name": "epoch", "type": "U64"}, {"name": "amount", "type": "U64"}]}, "UnstakingRequestEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "validator_address", "type": "Address"}, {"name": "staker_address", "type": "Address"}, {"name": "stake_activation_epoch", "type": "U64"}, {"name": "unstaking_epoch", "type": "U64"}, {"name": "principal_amount", "type": "U64"}, {"name": "reward_amount", "type": "U64"}]}, "Validator": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "metadata", "type": {"Struct": {"address": "0x3", "module": "validator", "name": "ValidatorMetadata", "typeArguments": []}}}, {"name": "voting_power", "type": "U64"}, {"name": "operation_cap_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "gas_price", "type": "U64"}, {"name": "staking_pool", "type": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}, {"name": "commission_rate", "type": "U64"}, {"name": "next_epoch_stake", "type": "U64"}, {"name": "next_epoch_gas_price", "type": "U64"}, {"name": "next_epoch_commission_rate", "type": "U64"}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}, "ValidatorMetadata": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "sui_address", "type": "Address"}, {"name": "protocol_pubkey_bytes", "type": {"Vector": "U8"}}, {"name": "network_pubkey_bytes", "type": {"Vector": "U8"}}, {"name": "worker_pubkey_bytes", "type": {"Vector": "U8"}}, {"name": "proof_of_possession", "type": {"Vector": "U8"}}, {"name": "name", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "description", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "image_url", "type": {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}}, {"name": "project_url", "type": {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}}, {"name": "net_address", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "p2p_address", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "primary_address", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "worker_address", "type": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"name": "next_epoch_protocol_pubkey_bytes", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}, {"name": "next_epoch_proof_of_possession", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}, {"name": "next_epoch_network_pubkey_bytes", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}, {"name": "next_epoch_worker_pubkey_bytes", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}, {"name": "next_epoch_net_address", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, {"name": "next_epoch_p2p_address", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, {"name": "next_epoch_primary_address", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, {"name": "next_epoch_worker_address", "type": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}}, "exposedFunctions": {"activate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": []}, "adjust_stake_and_gas_price": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": []}, "commission_rate": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "convert_to_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "deactivate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": []}, "deposit_stake_rewards": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}], "return": []}, "description": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "effectuate_staged_metadata": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": []}, "gas_price": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "get_staking_pool_ref": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakingPool", "typeArguments": []}}}]}, "image_url": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}}]}, "is_duplicate": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["Bool"]}, "is_preactive": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["Bool"]}, "metadata": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "ValidatorMetadata", "typeArguments": []}}}]}, "name": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "network_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "network_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "new": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": ["Address", {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}]}, "new_metadata": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": ["Address", {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Vector": "U8"}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}, {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}, {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}], "return": [{"Struct": {"address": "0x3", "module": "validator", "name": "ValidatorMetadata", "typeArguments": []}}]}, "new_unverified_validator_operation_cap_and_transfer": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "next_epoch_gas_price": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "next_epoch_network_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}]}, "next_epoch_network_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}]}, "next_epoch_p2p_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}]}, "next_epoch_primary_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}]}, "next_epoch_proof_of_possession": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}]}, "next_epoch_protocol_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}]}, "next_epoch_worker_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}]}, "next_epoch_worker_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Vector": "U8"}]}}}]}, "operation_cap_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}]}, "p2p_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "pending_stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "pending_stake_withdraw_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "pool_token_exchange_rate_at_epoch": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}, "primary_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "process_pending_stakes_and_withdraws": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "project_url": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "url", "name": "Url", "typeArguments": []}}}]}, "proof_of_possession": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "protocol_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "redeem_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "request_add_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_add_stake_at_genesis": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, "Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_commission_rate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": []}, "request_set_gas_price": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}, "U64"], "return": []}, "request_withdraw_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "set_candidate_commission_rate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": []}, "set_candidate_gas_price": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}, "U64"], "return": []}, "set_voting_power": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, "U64"], "return": []}, "stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "staking_pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "sui_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["Address"]}, "total_stake": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "total_stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "update_candidate_network_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_candidate_network_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_candidate_p2p_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_candidate_primary_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_candidate_protocol_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}], "return": []}, "update_candidate_worker_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_candidate_worker_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_description": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_image_url": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_name": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_network_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_network_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_p2p_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_primary_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_protocol_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}, {"Vector": "U8"}], "return": []}, "update_next_epoch_worker_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_next_epoch_worker_pubkey": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "update_project_url": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "validate_metadata": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "ValidatorMetadata", "typeArguments": []}}}], "return": []}, "validate_metadata_bcs": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": []}, "voting_power": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["U64"]}, "worker_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}]}, "worker_pubkey_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}}}, "validator_cap": {"fileFormatVersion": 6, "address": "0x3", "name": "validator_cap", "friends": [{"address": "0x3", "name": "sui_system_state_inner"}, {"address": "0x3", "name": "validator"}, {"address": "0x3", "name": "validator_set"}], "structs": {"UnverifiedValidatorOperationCap": {"abilities": {"abilities": ["Store", "Key"]}, "typeParameters": [], "fields": [{"name": "id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "UID", "typeArguments": []}}}, {"name": "authorizer_validator_address", "type": "Address"}]}, "ValidatorOperationCap": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "authorizer_validator_address", "type": "Address"}]}}, "exposedFunctions": {"into_verified": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}]}, "new_unverified_validator_operation_cap_and_transfer": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": ["Address", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "unverified_operation_cap_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}], "return": [{"Reference": "Address"}]}, "verified_operation_cap_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}}], "return": [{"Reference": "Address"}]}}}, "validator_set": {"fileFormatVersion": 6, "address": "0x3", "name": "validator_set", "friends": [{"address": "0x3", "name": "genesis"}, {"address": "0x3", "name": "sui_system_state_inner"}], "structs": {"ValidatorEpochInfoEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "validator_address", "type": "Address"}, {"name": "reference_gas_survey_quote", "type": "U64"}, {"name": "stake", "type": "U64"}, {"name": "commission_rate", "type": "U64"}, {"name": "pool_staking_reward", "type": "U64"}, {"name": "storage_fund_staking_reward", "type": "U64"}, {"name": "pool_token_exchange_rate", "type": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}}, {"name": "tallying_rule_reporters", "type": {"Vector": "Address"}}, {"name": "tallying_rule_global_score", "type": "U64"}]}, "ValidatorEpochInfoEventV2": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "validator_address", "type": "Address"}, {"name": "reference_gas_survey_quote", "type": "U64"}, {"name": "stake", "type": "U64"}, {"name": "voting_power", "type": "U64"}, {"name": "commission_rate", "type": "U64"}, {"name": "pool_staking_reward", "type": "U64"}, {"name": "storage_fund_staking_reward", "type": "U64"}, {"name": "pool_token_exchange_rate", "type": {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}}, {"name": "tallying_rule_reporters", "type": {"Vector": "Address"}}, {"name": "tallying_rule_global_score", "type": "U64"}]}, "ValidatorJoinEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "validator_address", "type": "Address"}, {"name": "staking_pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}]}, "ValidatorLeaveEvent": {"abilities": {"abilities": ["Copy", "Drop"]}, "typeParameters": [], "fields": [{"name": "epoch", "type": "U64"}, {"name": "validator_address", "type": "Address"}, {"name": "staking_pool_id", "type": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}, {"name": "is_voluntary", "type": "Bool"}]}, "ValidatorSet": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "total_stake", "type": "U64"}, {"name": "active_validators", "type": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}, {"name": "pending_active_validators", "type": {"Struct": {"address": "0x2", "module": "table_vec", "name": "TableVec", "typeArguments": [{"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}]}}}, {"name": "pending_removals", "type": {"Vector": "U64"}}, {"name": "staking_pool_mappings", "type": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}, "Address"]}}}, {"name": "inactive_validators", "type": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}, {"Struct": {"address": "0x3", "module": "validator_wrapper", "name": "ValidatorWrapper", "typeArguments": []}}]}}}, {"name": "validator_candidates", "type": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["Address", {"Struct": {"address": "0x3", "module": "validator_wrapper", "name": "ValidatorWrapper", "typeArguments": []}}]}}}, {"name": "at_risk_validators", "type": {"Struct": {"address": "0x2", "module": "vec_map", "name": "VecMap", "typeArguments": ["Address", "U64"]}}}, {"name": "extra_fields", "type": {"Struct": {"address": "0x2", "module": "bag", "name": "Bag", "typeArguments": []}}}]}, "VotingPowerAdmissionStartEpochKey": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "dummy_field", "type": "Bool"}]}}, "exposedFunctions": {"active_validator_addresses": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": [{"Vector": "Address"}]}, "active_validators": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": [{"Reference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}]}, "advance_epoch": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "vec_map", "name": "VecMap", "typeArguments": ["Address", {"Struct": {"address": "0x2", "module": "vec_set", "name": "VecSet", "typeArguments": ["Address"]}}]}}}, "U64", "U64", {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "assert_no_pending_or_active_duplicates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": []}, "calculate_total_stakes": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}], "return": ["U64"]}, "convert_to_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}]}, "derive_reference_gas_price": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": ["U64"]}, "get_active_or_pending_or_candidate_validator_ref": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address", "U8"], "return": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_active_validator_ref": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_pending_validator_ref": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": [{"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_validator_mut": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}, "Address"], "return": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_validator_mut_with_ctx": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_validator_mut_with_ctx_including_candidates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "get_validator_mut_with_verified_cap": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}}, "Bool"], "return": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}, "is_active_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["Bool"]}, "is_active_validator_by_sui_address": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["Bool"]}, "is_at_risk_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["Bool"]}, "is_duplicate_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}], "return": ["Bool"]}, "is_inactive_validator": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}], "return": ["Bool"]}, "is_validator_candidate": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["Bool"]}, "new": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}]}, "next_epoch_validator_count": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": ["U64"]}, "pool_exchange_rates": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": ["U64", {"Struct": {"address": "0x3", "module": "staking_pool", "name": "PoolTokenExchangeRate", "typeArguments": []}}]}}}]}, "redeem_fungible_staked_sui": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "FungibleStakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "request_add_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address", {"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}]}, "request_add_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_add_validator_candidate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_remove_validator_candidate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_set_commission_rate": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "U64", {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": []}, "request_withdraw_stake": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Struct": {"address": "0x3", "module": "staking_pool", "name": "StakedSui", "typeArguments": []}}, {"Reference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x2", "module": "balance", "name": "Balance", "typeArguments": [{"Struct": {"address": "0x2", "module": "sui", "name": "SUI", "typeArguments": []}}]}}]}, "staking_pool_mappings": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x2", "module": "table", "name": "Table", "typeArguments": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}, "Address"]}}}]}, "sum_voting_power_by_addresses": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}, {"Reference": {"Vector": "Address"}}], "return": ["U64"]}, "total_stake": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}], "return": ["U64"]}, "validator_address_by_pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}}], "return": ["Address"]}, "validator_stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["U64"]}, "validator_staking_pool_id": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": [{"Struct": {"address": "0x2", "module": "object", "name": "ID", "typeArguments": []}}]}, "validator_total_stake_amount": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["U64"]}, "validator_voting_power": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, "Address"], "return": ["U64"]}, "verify_cap": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_set", "name": "ValidatorSet", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x3", "module": "validator_cap", "name": "UnverifiedValidatorOperationCap", "typeArguments": []}}}, "U8"], "return": [{"Struct": {"address": "0x3", "module": "validator_cap", "name": "ValidatorOperationCap", "typeArguments": []}}]}}}, "validator_wrapper": {"fileFormatVersion": 6, "address": "0x3", "name": "validator_wrapper", "friends": [{"address": "0x3", "name": "validator_set"}], "structs": {"ValidatorWrapper": {"abilities": {"abilities": ["Store"]}, "typeParameters": [], "fields": [{"name": "inner", "type": {"Struct": {"address": "0x2", "module": "versioned", "name": "Versioned", "typeArguments": []}}}]}}, "exposedFunctions": {"create_v1": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}, {"MutableReference": {"Struct": {"address": "0x2", "module": "tx_context", "name": "TxContext", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x3", "module": "validator_wrapper", "name": "ValidatorWrapper", "typeArguments": []}}]}, "destroy": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x3", "module": "validator_wrapper", "name": "ValidatorWrapper", "typeArguments": []}}], "return": [{"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}]}, "load_validator_maybe_upgrade": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator_wrapper", "name": "ValidatorWrapper", "typeArguments": []}}}], "return": [{"MutableReference": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}]}}}, "voting_power": {"fileFormatVersion": 6, "address": "0x3", "name": "voting_power", "friends": [{"address": "0x3", "name": "validator_set"}], "structs": {"VotingPowerInfo": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "validator_index", "type": "U64"}, {"name": "voting_power", "type": "U64"}]}, "VotingPowerInfoV2": {"abilities": {"abilities": ["Drop"]}, "typeParameters": [], "fields": [{"name": "validator_index", "type": "U64"}, {"name": "voting_power", "type": "U64"}, {"name": "stake", "type": "U64"}]}}, "exposedFunctions": {"derive_raw_voting_power": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": ["U64"]}, "quorum_threshold": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [], "return": ["U64"]}, "set_voting_power": {"visibility": "Friend", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Vector": {"Struct": {"address": "0x3", "module": "validator", "name": "Validator", "typeArguments": []}}}}, "U64"], "return": []}, "total_voting_power": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [], "return": ["U64"]}}}}