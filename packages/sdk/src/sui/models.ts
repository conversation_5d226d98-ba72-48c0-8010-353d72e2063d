import type { SuiEvent, MoveCallSuiTransaction, SuiMoveObject } from '@mysten/sui/client'
import { DecodedStruct } from '@typemove/move'
import { MoveFetchConfig } from '@sentio/protos'

export type TypedEventInstance<T> = DecodedStruct<SuiEvent, T>
export type TypedSuiMoveObject<T> = DecodedStruct<SuiMoveObject, T>

export type TypedFunctionPayload<T extends Array<any>> = MoveCallSuiTransaction & {
  /**
   * decoded argument data using ABI, undefined if there is decoding error, usually because the ABI/data mismatch
   */
  arguments_decoded: T
}

export type PartitionHandler<D> = (data: D) => string | Promise<string>

export type HandlerOptions<F, D> = Partial<F> & {
  partitionKey?: string | PartitionHandler<D>
}

/**
 * Merge two handler options, with the second options taking precedence over the first.
 * @param options1 First handler options
 * @param options2 Second handler options (takes precedence)
 * @returns Merged handler options
 */
export function mergeHandlerOptions<F, D>(
  options1?: HandlerOptions<F, D>,
  options2?: HandlerOptions<F, D>
): HandlerOptions<F, D> | undefined {
  if (!options1 && !options2) return undefined
  if (!options1) return options2
  if (!options2) return options1

  return {
    ...options1,
    ...options2,
    // For partitionKey, the second option takes precedence
    partitionKey: options2.partitionKey ?? options1.partitionKey
  }
}
