{"address": {"fileFormatVersion": 6, "address": "0x1", "name": "address", "friends": [], "structs": {}, "exposedFunctions": {"length": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [], "return": ["U64"]}}}, "ascii": {"fileFormatVersion": 6, "address": "0x1", "name": "ascii", "friends": [], "structs": {"Char": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "byte", "type": "U8"}]}, "String": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "bytes", "type": {"Vector": "U8"}}]}}, "exposedFunctions": {"all_characters_printable": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": ["Bool"]}, "append": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}, {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}], "return": []}, "as_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "byte": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "ascii", "name": "Char", "typeArguments": []}}], "return": ["U8"]}, "char": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "Char", "typeArguments": []}}]}, "index_of": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": ["U64"]}, "insert": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}, "U64", {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}], "return": []}, "into_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}], "return": [{"Vector": "U8"}]}, "is_empty": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": ["Bool"]}, "is_printable_char": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": ["Bool"]}, "is_valid_char": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": ["Bool"]}, "length": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": ["U64"]}, "pop_char": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "Char", "typeArguments": []}}]}, "push_char": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}, {"Struct": {"address": "0x1", "module": "ascii", "name": "Char", "typeArguments": []}}], "return": []}, "string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "substring": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}, "U64", "U64"], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "to_lowercase": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "to_uppercase": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "try_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}}]}}}, "bcs": {"fileFormatVersion": 6, "address": "0x1", "name": "bcs", "friends": [], "structs": {}, "exposedFunctions": {"to_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"TypeParameter": 0}}], "return": [{"Vector": "U8"}]}}}, "bit_vector": {"fileFormatVersion": 6, "address": "0x1", "name": "bit_vector", "friends": [], "structs": {"BitVector": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "length", "type": "U64"}, {"name": "bit_field", "type": {"Vector": "Bool"}}]}}, "exposedFunctions": {"is_index_set": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}, "U64"], "return": ["Bool"]}, "length": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}], "return": ["U64"]}, "longest_set_sequence_starting_at": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}, "U64"], "return": ["U64"]}, "new": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}]}, "set": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}, "U64"], "return": []}, "shift_left": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}, "U64"], "return": []}, "unset": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "bit_vector", "name": "BitVector", "typeArguments": []}}}, "U64"], "return": []}}}, "bool": {"fileFormatVersion": 6, "address": "0x1", "name": "bool", "friends": [], "structs": {}, "exposedFunctions": {}}, "debug": {"fileFormatVersion": 6, "address": "0x1", "name": "debug", "friends": [], "structs": {}, "exposedFunctions": {"print": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"TypeParameter": 0}}], "return": []}, "print_stack_trace": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [], "return": []}}}, "fixed_point32": {"fileFormatVersion": 6, "address": "0x1", "name": "fixed_point32", "friends": [], "structs": {"FixedPoint32": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "value", "type": "U64"}]}}, "exposedFunctions": {"create_from_rational": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": [{"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}]}, "create_from_raw_value": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}]}, "divide_u64": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", {"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}], "return": ["U64"]}, "get_raw_value": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}], "return": ["U64"]}, "is_zero": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}], "return": ["Bool"]}, "multiply_u64": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", {"Struct": {"address": "0x1", "module": "fixed_point32", "name": "FixedPoint32", "typeArguments": []}}], "return": ["U64"]}}}, "hash": {"fileFormatVersion": 6, "address": "0x1", "name": "hash", "friends": [], "structs": {}, "exposedFunctions": {"sha2_256": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Vector": "U8"}]}, "sha3_256": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Vector": "U8"}]}}}, "macros": {"fileFormatVersion": 6, "address": "0x1", "name": "macros", "friends": [], "structs": {}, "exposedFunctions": {}}, "option": {"fileFormatVersion": 6, "address": "0x1", "name": "option", "friends": [], "structs": {"Option": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [{"constraints": {"abilities": []}, "isPhantom": false}], "fields": [{"name": "vec", "type": {"Vector": {"TypeParameter": 0}}}]}}, "exposedFunctions": {"borrow": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}], "return": [{"Reference": {"TypeParameter": 0}}]}, "borrow_mut": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}], "return": [{"MutableReference": {"TypeParameter": 0}}]}, "borrow_with_default": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"Reference": {"TypeParameter": 0}}], "return": [{"Reference": {"TypeParameter": 0}}]}, "contains": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"Reference": {"TypeParameter": 0}}], "return": ["Bool"]}, "destroy_none": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}], "return": []}, "destroy_some": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}], "return": [{"TypeParameter": 0}]}, "destroy_with_default": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": ["Drop"]}], "parameters": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}, {"TypeParameter": 0}], "return": [{"TypeParameter": 0}]}, "extract": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}], "return": [{"TypeParameter": 0}]}, "fill": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"TypeParameter": 0}], "return": []}, "get_with_default": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": ["Copy", "Drop"]}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"TypeParameter": 0}], "return": [{"TypeParameter": 0}]}, "is_none": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}], "return": ["Bool"]}, "is_some": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}], "return": ["Bool"]}, "none": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}]}, "some": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"TypeParameter": 0}], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}]}, "swap": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"TypeParameter": 0}], "return": [{"TypeParameter": 0}]}, "swap_or_fill": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}}, {"TypeParameter": 0}], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}]}, "to_vec": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"TypeParameter": 0}]}}], "return": [{"Vector": {"TypeParameter": 0}}]}}}, "string": {"fileFormatVersion": 6, "address": "0x1", "name": "string", "friends": [], "structs": {"String": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "bytes", "type": {"Vector": "U8"}}]}}, "exposedFunctions": {"append": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}], "return": []}, "append_utf8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"Vector": "U8"}], "return": []}, "as_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}], "return": [{"Reference": {"Vector": "U8"}}]}, "from_ascii": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "index_of": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, {"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}], "return": ["U64"]}, "insert": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"MutableReference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, "U64", {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}], "return": []}, "into_bytes": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}], "return": [{"Vector": "U8"}]}, "is_empty": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}], "return": ["Bool"]}, "length": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}], "return": ["U64"]}, "sub_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, "U64", "U64"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "substring": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}}, "U64", "U64"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "to_ascii": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "try_utf8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}]}, "utf8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Vector": "U8"}], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, "type_name": {"fileFormatVersion": 6, "address": "0x1", "name": "type_name", "friends": [], "structs": {"TypeName": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "name", "type": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}]}}, "exposedFunctions": {"borrow_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}}], "return": [{"Reference": {"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}}]}, "get": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [], "return": [{"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}]}, "get_address": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "get_module": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "get_with_original_ids": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [], "return": [{"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}]}, "into_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "ascii", "name": "String", "typeArguments": []}}]}, "is_primitive": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Reference": {"Struct": {"address": "0x1", "module": "type_name", "name": "TypeName", "typeArguments": []}}}], "return": ["Bool"]}}}, "u128": {"fileFormatVersion": 6, "address": "0x1", "name": "u128", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": ["U128"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U128"], "return": ["U128"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U128"], "return": ["U128"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U128"], "return": ["U128"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U128"], "return": ["U128"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U8"], "return": ["U128"]}, "sqrt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": ["U128"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "try_as_u16": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U16"]}}]}, "try_as_u32": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U32"]}}]}, "try_as_u64": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}]}, "try_as_u8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U8"]}}]}}}, "u16": {"fileFormatVersion": 6, "address": "0x1", "name": "u16", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16"], "return": ["U16"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16", "U16"], "return": ["U16"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16", "U16"], "return": ["U16"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16", "U16"], "return": ["U16"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16", "U16"], "return": ["U16"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16", "U8"], "return": ["U16"]}, "sqrt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16"], "return": ["U16"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "try_as_u8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U16"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U8"]}}]}}}, "u256": {"fileFormatVersion": 6, "address": "0x1", "name": "u256", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": ["U256"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256", "U256"], "return": ["U256"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256", "U256"], "return": ["U256"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256", "U256"], "return": ["U256"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256", "U256"], "return": ["U256"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256", "U8"], "return": ["U256"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "try_as_u128": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U128"]}}]}, "try_as_u16": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U16"]}}]}, "try_as_u32": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U32"]}}]}, "try_as_u64": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U64"]}}]}, "try_as_u8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U256"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U8"]}}]}}}, "u32": {"fileFormatVersion": 6, "address": "0x1", "name": "u32", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": ["U32"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32", "U32"], "return": ["U32"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32", "U32"], "return": ["U32"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32", "U32"], "return": ["U32"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32", "U32"], "return": ["U32"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32", "U8"], "return": ["U32"]}, "sqrt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": ["U32"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "try_as_u16": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U16"]}}]}, "try_as_u8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U8"]}}]}}}, "u64": {"fileFormatVersion": 6, "address": "0x1", "name": "u64", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": ["U64"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": ["U64"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": ["U64"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": ["U64"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": ["U64"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U8"], "return": ["U64"]}, "sqrt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": ["U64"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}, "try_as_u16": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U16"]}}]}, "try_as_u32": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U32"]}}]}, "try_as_u8": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "option", "name": "Option", "typeArguments": ["U8"]}}]}}}, "u8": {"fileFormatVersion": 6, "address": "0x1", "name": "u8", "friends": [], "structs": {}, "exposedFunctions": {"bitwise_not": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": ["U8"]}, "diff": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8", "U8"], "return": ["U8"]}, "divide_and_round_up": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8", "U8"], "return": ["U8"]}, "max": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8", "U8"], "return": ["U8"]}, "min": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8", "U8"], "return": ["U8"]}, "pow": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8", "U8"], "return": ["U8"]}, "sqrt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": ["U8"]}, "to_string": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U8"], "return": [{"Struct": {"address": "0x1", "module": "string", "name": "String", "typeArguments": []}}]}}}, "uq32_32": {"fileFormatVersion": 6, "address": "0x1", "name": "uq32_32", "friends": [], "structs": {"UQ32_32": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "pos0", "type": "U64"}]}}, "exposedFunctions": {"add": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "div": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "from_int": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U32"], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "from_quotient": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", "U64"], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "from_raw": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "ge": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["Bool"]}, "gt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["Bool"]}, "int_div": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["U64"]}, "int_mul": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64", {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["U64"]}, "le": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["Bool"]}, "lt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["Bool"]}, "mul": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "sub": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}]}, "to_int": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["U32"]}, "to_raw": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq32_32", "name": "UQ32_32", "typeArguments": []}}], "return": ["U64"]}}}, "uq64_64": {"fileFormatVersion": 6, "address": "0x1", "name": "uq64_64", "friends": [], "structs": {"UQ64_64": {"abilities": {"abilities": ["Copy", "Drop", "Store"]}, "typeParameters": [], "fields": [{"name": "pos0", "type": "U128"}]}}, "exposedFunctions": {"add": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "div": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "from_int": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U64"], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "from_quotient": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", "U128"], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "from_raw": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128"], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "ge": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["Bool"]}, "gt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["Bool"]}, "int_div": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["U128"]}, "int_mul": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": ["U128", {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["U128"]}, "le": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["Bool"]}, "lt": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["Bool"]}, "mul": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "sub": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}, {"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}]}, "to_int": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["U64"]}, "to_raw": {"visibility": "Public", "isEntry": false, "typeParameters": [], "parameters": [{"Struct": {"address": "0x1", "module": "uq64_64", "name": "UQ64_64", "typeArguments": []}}], "return": ["U128"]}}}, "vector": {"fileFormatVersion": 6, "address": "0x1", "name": "vector", "friends": [], "structs": {}, "exposedFunctions": {"append": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, {"Vector": {"TypeParameter": 0}}], "return": []}, "borrow": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Vector": {"TypeParameter": 0}}}, "U64"], "return": [{"Reference": {"TypeParameter": 0}}]}, "borrow_mut": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, "U64"], "return": [{"MutableReference": {"TypeParameter": 0}}]}, "contains": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Vector": {"TypeParameter": 0}}}, {"Reference": {"TypeParameter": 0}}], "return": ["Bool"]}, "destroy_empty": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Vector": {"TypeParameter": 0}}], "return": []}, "empty": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [], "return": [{"Vector": {"TypeParameter": 0}}]}, "flatten": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Vector": {"Vector": {"TypeParameter": 0}}}], "return": [{"Vector": {"TypeParameter": 0}}]}, "index_of": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Vector": {"TypeParameter": 0}}}, {"Reference": {"TypeParameter": 0}}], "return": ["Bool", "U64"]}, "insert": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, {"TypeParameter": 0}, "U64"], "return": []}, "is_empty": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Vector": {"TypeParameter": 0}}}], "return": ["Bool"]}, "length": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"Reference": {"Vector": {"TypeParameter": 0}}}], "return": ["U64"]}, "pop_back": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}], "return": [{"TypeParameter": 0}]}, "push_back": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, {"TypeParameter": 0}], "return": []}, "remove": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, "U64"], "return": [{"TypeParameter": 0}]}, "reverse": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}], "return": []}, "singleton": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"TypeParameter": 0}], "return": [{"Vector": {"TypeParameter": 0}}]}, "swap": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, "U64", "U64"], "return": []}, "swap_remove": {"visibility": "Public", "isEntry": false, "typeParameters": [{"abilities": []}], "parameters": [{"MutableReference": {"Vector": {"TypeParameter": 0}}}, "U64"], "return": [{"TypeParameter": 0}]}}}}