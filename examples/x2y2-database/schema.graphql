type Reward @entity(timeseries: true) {
  id: Int8!
  timestamp: Timestamp!
  value: BigDecimal!
  phase: String
  owner: User!
}

type Transfer @entity {
  id: ID!
  from: User!
  to: User!
  amount: BigDecimal!
  intValue: Int
  bigIntValue: BigInt!
  stringValue: String
  floatValue: Float
  booleanValue: Boolean
  arrayValue: [String]!
  dateValue: Timestamp

}

type User @entity {
  id: ID!
  name: String!
  rewards: [Reward!]! @derivedFrom(field: "owner")
}

type Token @entity {
  id: ID!
  value: BigDecimal!
}

